@rendermode InteractiveServer
@inject NavigationManager NavigationManager

<AuthorizeView>
    <Authorized>
        <RadzenHeader Class="align-items-center justify-content-between">
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center"
                         JustifyContent="JustifyContent.SpaceBetween" Gap="0">
                <RadzenSidebarToggle Click="@(() => _sidebar1Expanded = !_sidebar1Expanded)"/>
                <RadzenLabel Text="ISP Care - Cloud Core"/>
                <RadzenButton Text="Logout" Click="@(() => NavigationManager.NavigateTo("/logout"))"
                              ButtonStyle="ButtonStyle.Info"/>

            </RadzenStack>
        </RadzenHeader>
    </Authorized>
</AuthorizeView>
<RadzenSidebar @bind-Expanded="@_sidebar1Expanded">
    <RadzenPanelMenu>
        <RadzenPanelMenuItem Text="Home" Icon="home"/>
        <RadzenPanelMenuItem Text="ISPs" Icon="alternate_email"/>
        <RadzenPanelMenuItem Text="POS Users" Icon="account_box"/>
    </RadzenPanelMenu>
</RadzenSidebar>

@code {
    bool _sidebar1Expanded = false;
}
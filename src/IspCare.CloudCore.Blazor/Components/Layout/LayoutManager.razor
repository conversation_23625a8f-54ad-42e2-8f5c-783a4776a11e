@rendermode InteractiveServer

<RadzenLayout>
    <RadzenHeader>
        <div class="d-flex align-items-center justify-content-between p-4">
            <RadzenButton Icon="menu" Click="@ToggleSidebar" Variant="Variant.Text"/>
            <RadzenLabel Text="ISP Care - Cloud Core" Style="font-size: 1.5rem; margin-left: 12px;"/>
            <AuthorizeView>
                <Authorized>
                    <RadzenButton Text="Logout" Click="@(() => Navigation.NavigateTo("/logout"))"
                                  ButtonStyle="ButtonStyle.Info"/>
                </Authorized>
                <NotAuthorized>
                    <RadzenButton Text="Login" Click="@(args => Console.WriteLine("hi"))"
                                  ButtonStyle="ButtonStyle.Success"/>
                </NotAuthorized>
            </AuthorizeView>
        </div>
    </RadzenHeader>
    <RadzenSidebar @bind-Expanded="@_sidebarExpanded">
        <RadzenPanelMenu>
            <RadzenPanelMenuItem Text="Home" Path="/"/>
            <AuthorizeView>
                <Authorized>
                    <RadzenPanelMenuItem Text="ISPs" Path="/isps"/>
                    <RadzenPanelMenuItem Text="POS Users" Path="/pos-users"/>
                </Authorized>
            </AuthorizeView>
        </RadzenPanelMenu>
    </RadzenSidebar>
    <RadzenContent>
        <RadzenStack Gap="1rem" Class="rz-p-4">
            @ChildContent
        </RadzenStack>
    </RadzenContent>
</RadzenLayout>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    
    [Inject] private NavigationManager Navigation { get; set; } = default!;
    
    private bool _sidebarExpanded = true;

    private void ToggleSidebar()
    {
        Console.WriteLine($"ToggleSidebar called, current state: {_sidebarExpanded}");
        _sidebarExpanded = !_sidebarExpanded;
        Console.WriteLine($"New state: {_sidebarExpanded}");
        StateHasChanged();
    }
}

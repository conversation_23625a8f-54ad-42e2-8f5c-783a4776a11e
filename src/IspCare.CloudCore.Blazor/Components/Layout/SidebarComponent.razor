@rendermode InteractiveServer

<RadzenPanelMenu>
    <RadzenPanelMenuItem Text="Home" Path="/"/>
    <AuthorizeView>
        <Authorized>
            <RadzenPanelMenuItem Text="ISPs" Path="/isps"/>
            <RadzenPanelMenuItem Text="POS Users" Path="/pos-users"/>
        </Authorized>
    </AuthorizeView>
</RadzenPanelMenu>

@code {
    // No additional logic needed for now, but this component is interactive
}

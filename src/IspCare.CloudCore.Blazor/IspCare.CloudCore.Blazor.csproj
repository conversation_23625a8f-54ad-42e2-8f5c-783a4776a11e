<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\IspCare.CloudCore.Application\IspCare.CloudCore.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Radzen.Blazor" Version="7.1.2" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Services\SidebarStateService.cs" />
  </ItemGroup>

</Project>

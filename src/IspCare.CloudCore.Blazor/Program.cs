using IspCare.CloudCore.Application.Isps;
using IspCare.CloudCore.Application.PosUsers;
using IspCare.CloudCore.Blazor.Components;
using IspCare.CloudCore.Blazor.Components.Layout;
using IspCare.CloudCore.Domain.Abstractions;
using IspCare.CloudCore.Domain.AdminUsers;
using IspCare.CloudCore.Domain.Cache;
using IspCare.CloudCore.Domain.Isps;
using IspCare.CloudCore.Domain.PosUsers;
using IspCare.CloudCore.Infrastructure.Data;
using IspCare.CloudCore.Infrastructure.Implementations;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using <PERSON><PERSON>zen;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

builder.Services.AddRadzenComponents();

builder.Services.AddTransient<IIspAppService, IspAppService>();
builder.Services.AddTransient<IIspManager, IspManager>();

builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();

builder.Services.AddDbContext<CloudCoreDbContext>(options =>
    options.UseNpgsql(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        op => op.MigrationsAssembly(typeof(CloudCoreDbContext).Assembly.FullName)
    )
);

builder.Services.AddTransient<IIspAppService, IspAppService>();
builder.Services.AddTransient<IIspManager, IspManager>();
builder.Services.AddTransient<IPosUserAppService, PosUserAppService>();
builder.Services.AddTransient<PosUserManager>();
builder.Services.AddTransient<SessionCacheManager>();
builder.Services.AddDistributedMemoryCache();
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

builder.Services.AddIdentityCore<AdminUser>()
    .AddEntityFrameworkStores<CloudCoreDbContext>()
    .AddDefaultTokenProviders();

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options => 
    {
        options.LoginPath = "/login";
    });
builder.Services.AddAuthorization();
builder.Services.AddCascadingAuthenticationState();
builder.Services.AddHttpContextAccessor();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();
app.UseAuthentication();
app.UseAuthorization();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
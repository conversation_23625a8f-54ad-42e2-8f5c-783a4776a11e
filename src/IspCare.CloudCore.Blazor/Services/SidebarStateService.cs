namespace IspCare.CloudCore.Blazor.Services;

public class SidebarStateService
{
    private bool _isExpanded = true;
    
    public bool IsExpanded => _isExpanded;
    
    public event Action? OnStateChanged;
    
    public void ToggleSidebar()
    {
        _isExpanded = !_isExpanded;
        OnStateChanged?.Invoke();
    }
    
    public void SetExpanded(bool expanded)
    {
        if (_isExpanded != expanded)
        {
            _isExpanded = expanded;
            OnStateChanged?.Invoke();
        }
    }
}
